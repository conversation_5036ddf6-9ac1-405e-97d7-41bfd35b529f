# Monika 个人记账软件 - 启动指南

## 项目概述
Monika是一个基于FastAPI + Vue.js + SQLite的个人记账软件，提供完整的财务管理功能。

## 项目结构
```
Monika/
├── backend/                 # 后端代码
│   ├── app/                # FastAPI应用
│   ├── models/             # 数据库模型
│   ├── schemas/            # Pydantic数据验证
│   ├── crud/               # 数据库操作
│   ├── auth/               # 认证相关
│   ├── api/                # API路由
│   └── database/           # 数据库配置
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 通用组件
│   │   ├── stores/         # 状态管理
│   │   ├── router/         # 路由配置
│   │   └── api/            # API配置
├── venv/                   # Python虚拟环境
├── run_backend.py          # 后端启动脚本
└── README.md               # 项目说明
```

## 启动步骤

### 1. 启动后端服务器
```bash
# 激活Python虚拟环境
source venv/bin/activate

# 启动后端服务器
PYTHONPATH=. python run_backend.py
```

后端服务器将在 http://127.0.0.1:8000 启动

### 2. 安装前端依赖并启动前端服务器
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务器将在 http://localhost:3000 启动

## 功能特性

### 已实现的功能
- ✅ 用户注册和登录
- ✅ 仪表盘（财务概览）
- ✅ 交易记录管理（增删改查）
- ✅ 账户管理（支持多种账户类型）
- ✅ 项目管理（按项目分组管理交易）
- ✅ 默认分类系统（收入/支出分类）
- ✅ JWT认证和权限控制
- ✅ 响应式设计

### 核心功能说明
1. **账务管理**: 以"账务项目"和"账务条目"两个层级管理财务记录
2. **分类系统**: 提供预设的收入/支出分类
3. **多账户支持**: 支持储蓄卡、信用卡、现金、支付宝、微信等账户类型
4. **数据统计**: 实时计算总收入、总支出、净收入等统计数据

## API文档
后端启动后，可以访问以下地址查看API文档：
- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc

## 数据库
项目使用SQLite数据库，数据库文件为 `monika.db`，包含以下表：
- users: 用户表
- accounts: 账户表
- projects: 项目表
- categories: 分类表
- transactions: 交易表
- tags: 标签表
- transaction_tags: 交易标签关联表
- budgets: 预算表

## 技术栈
- **后端**: FastAPI, SQLAlchemy, SQLite, JWT认证
- **前端**: Vue.js 3, Vite, Pinia, Vue Router, Axios
- **开发工具**: Python venv, npm

## 下一步开发建议
1. 添加预算管理功能的前端界面
2. 实现标签系统的前端管理
3. 添加数据可视化图表
4. 实现数据导入/导出功能
5. 添加移动端适配
6. 实现数据备份功能

## 故障排除
1. 如果后端启动失败，检查Python虚拟环境是否正确激活
2. 如果前端依赖安装失败，尝试使用不同的npm镜像源
3. 如果API请求失败，检查后端服务器是否正在运行
4. 数据库相关错误，删除 `monika.db` 文件重新初始化
